package oryx.mixin;

import com.llamalad7.mixinextras.sugar.Local;
import com.mojang.brigadier.ParseResults;
import com.mojang.brigadier.StringReader;
import com.mojang.brigadier.suggestion.Suggestions;
import net.minecraft.client.gui.screen.ChatInputSuggestor;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.command.CommandSource;
import org.jetbrains.annotations.Nullable;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.LocalCapture;
import oryx.event.EventManager;
import oryx.event.impl.InputSuggestionEvent;

import java.util.concurrent.CompletableFuture;

@Mixin(ChatInputSuggestor.class)
public abstract class ChatInputSuggestorMixin {

	@Shadow
	@Final
	TextFieldWidget textField;
	@Shadow
	boolean completingSuggestions;
	@Shadow
	private @Nullable ParseResults<CommandSource> parse;
	@Shadow
	@Nullable
	private ChatInputSuggestor.@Nullable SuggestionWindow window;
	@Shadow
	private @Nullable CompletableFuture<Suggestions> pendingSuggestions;

	@Shadow
	protected abstract void showCommandSuggestions();

	@Inject(
			method = "refresh",
			at = @At(value = "INVOKE", target = "Lcom/mojang/brigadier/StringReader;canRead()Z", remap = false),
			cancellable = true
	)
	public void refresh(CallbackInfo ci, @Local StringReader reader) {
		var eventInputSuggestions = new InputSuggestionEvent(reader);
		EventManager.INSTANCE.post(eventInputSuggestions);

		if (eventInputSuggestions.getDispatcher() != null && eventInputSuggestions.getCommandSource() != null) {
			if (parse == null) parse = eventInputSuggestions.getDispatcher().parse(reader, eventInputSuggestions.getCommandSource());

			final int cursor = textField.getCursor();

			if (cursor >= 1 && (window == null || !completingSuggestions)) {
				pendingSuggestions = eventInputSuggestions.getDispatcher().getCompletionSuggestions(parse, cursor);
				pendingSuggestions.thenRun(() -> {if (pendingSuggestions.isDone()) showCommandSuggestions();});
			}
			ci.cancel();
		}
	}
}
