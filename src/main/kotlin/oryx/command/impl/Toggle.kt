package oryx.command.impl

import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.builder.LiteralArgumentBuilder
import net.minecraft.command.CommandSource
import oryx.command.Command
import oryx.module.ModuleManager
import oryx.utility.ChatUtil

class Toggle : Command("toggle") {
    override fun builder(builder: LiteralArgumentBuilder<CommandSource>) {
        builder.then(argument("module", StringArgumentType.greedyString())?.suggests { _, suggestionsBuilder ->
            ModuleManager.forEach {
                if (it.name.startsWith(suggestionsBuilder.remaining)) suggestionsBuilder.suggest(it.name)
            }
            return@suggests suggestionsBuilder.buildFuture()
        }?.executes {
            val moduleName = StringArgumentType.getString(it, "module")
            val module = ModuleManager.firstOrNull { module -> module.name == moduleName }
            if (module != null) {
                module.toggle()
                ChatUtil.send("[" + module.name + "] toggled " + if (module.enabled) "enabled" else "disabled")
                return@executes SUCCESS
            } else {
                ChatUtil.send("[$moduleName] does not exist")
                return@executes ERROR
            }
        })
    }
}