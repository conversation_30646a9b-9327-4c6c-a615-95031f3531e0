package oryx.command.impl

import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.builder.LiteralArgumentBuilder
import net.minecraft.command.CommandSource
import org.lwjgl.glfw.GLFW
import oryx.command.Command
import oryx.module.ModuleManager
import oryx.utility.ChatUtil

class Bind : Command("bind") {
    companion object {
        // List of valid GLFW key names (add more as needed)
        val keyNames = listOf(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
            "SPACE", "ENTER", "TAB", "ESCAPE", "LEFT_SHIFT", "RIGHT_SHIFT", "LEFT_CONTROL", "RIGHT_CONTROL", "LEFT_ALT", "RIGHT_ALT", "LEFT_SUPER", "RIGHT_SUPER",
            "UP", "DOWN", "LEFT", "RIGHT", "BACKSPACE", "DELETE", "INSERT", "HOME", "END", "PAGE_UP", "PAGE_DOWN"
        )
    }
    override fun builder(builder: LiteralArgumentBuilder<CommandSource>) {
        builder.then(
            argument("module", StringArgumentType.greedyString())?.suggests { _, suggestionsBuilder ->
                ModuleManager.forEach {
                    if (it.name.startsWith(suggestionsBuilder.remaining)) suggestionsBuilder.suggest(it.name)
                }
                suggestionsBuilder.buildFuture()
            }?.then(
                argument("key", StringArgumentType.word())
                    ?.suggests { _, suggestionsBuilder ->
                        // Suggest key names that start with the current input
                        val input = suggestionsBuilder.remaining.uppercase()
                        keyNames.filter { it.startsWith(input) }.forEach { suggestionsBuilder.suggest(it) }
                        suggestionsBuilder.buildFuture()
                    }
                    ?.executes {
                        val moduleName = StringArgumentType.getString(it, "module")
                        val keyName = StringArgumentType.getString(it, "key").uppercase()
                        val fieldName = "GLFW_KEY_$keyName"
                        val keyCode = try {
                            GLFW::class.java.getField(fieldName).getInt(null)
                        } catch (e: Exception) {
                            ChatUtil.send("Unknown key: $keyName")
                            return@executes ERROR
                        }
                        val module = ModuleManager.firstOrNull { module -> module.name == moduleName }
                        if (module != null) {
                            module.key = keyCode
                            ChatUtil.send("[${module.name}] bound to $keyName ($keyCode)")
                            return@executes SUCCESS
                        } else {
                            ChatUtil.send("[$moduleName] does not exist")
                            return@executes ERROR
                        }
                    }
            )
        )
    }
}
