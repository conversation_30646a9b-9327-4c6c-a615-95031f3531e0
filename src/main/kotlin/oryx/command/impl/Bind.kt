package oryx.command.impl

import com.mojang.brigadier.arguments.StringArgumentType
import com.mojang.brigadier.builder.LiteralArgumentBuilder
import net.minecraft.command.CommandSource
import oryx.command.Command
import oryx.module.ModuleManager
import oryx.utility.ChatUtil
import org.lwjgl.glfw.GLFW

class Bind : Command("bind") {
    override fun builder(builder: LiteralArgumentBuilder<CommandSource>) {
        builder.then(
            argument("module", StringArgumentType.greedyString())?.suggests { _, suggestionsBuilder ->
                ModuleManager.forEach {
                    if (it.name.startsWith(suggestionsBuilder.remaining)) suggestionsBuilder.suggest(it.name)
                }
                suggestionsBuilder.buildFuture()
            }?.then(
                // Accept key name (e.g. V, SPACE, ENTER) and translate to GLFW code
                argument("key", StringArgumentType.word())?.executes {
                    val moduleName = StringArgumentType.getString(it, "module")
                    val keyName = StringArgumentType.getString(it, "key").uppercase()
                    // Build field name in GLFW class
                    val fieldName = "GLFW_KEY_" + keyName
                    val keyCode = try {
                        GLFW::class.java.getField(fieldName).getInt(null)
                    } catch (e: Exception) {
                        ChatUtil.send("Unknown key: $keyName")
                        return@executes ERROR
                    }
                    val module = ModuleManager.firstOrNull { module -> module.name == moduleName }
                    if (module != null) {
                        module.key = keyCode
                        ChatUtil.send("[${module.name}] bound to $keyName ($keyCode)")
                        return@executes SUCCESS
                    } else {
                        ChatUtil.send("[$moduleName] does not exist")
                        return@executes ERROR
                    }
                }
            )
        )
    }
}