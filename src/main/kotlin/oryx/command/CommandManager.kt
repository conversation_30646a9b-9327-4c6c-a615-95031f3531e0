package oryx.command

import com.mojang.brigadier.CommandDispatcher
import com.mojang.brigadier.StringReader
import com.mojang.brigadier.exceptions.CommandSyntaxException
import net.minecraft.client.network.ClientCommandSource
import net.minecraft.command.CommandSource
import net.minecraft.screen.ScreenTexts
import net.minecraft.text.Text
import net.minecraft.text.Texts
import net.minecraft.util.Formatting
import oryx.command.impl.Bind
import oryx.command.impl.Toggle
import oryx.event.EventManager
import oryx.event.impl.ChatEvent
import oryx.event.impl.InputSuggestionEvent
import oryx.utility.ChatUtil
import oryx.utility.Manager
import kotlin.math.max

object CommandManager : Manager<Command>() {
    private const val PREFIX = "."
    private val dispatcher = CommandDispatcher<CommandSource>()
    private val commandSource = ClientCommandSource(null, mc)

    init {
        add(
            Toggle(),
            Bind()
        )

        EventManager.add(ChatEvent::class) {
            if (it.message.startsWith(PREFIX)) {
                it.cancelled = true

                val reader = StringReader(it.message)
                reader.cursor = PREFIX.length

                try {
                    dispatcher.execute(reader, commandSource)
                } catch (e: CommandSyntaxException) {
                    if (e.cursor >= 0) {
                        val i = e.input.length.coerceAtMost(e.cursor)
                        val mutableText = Text.empty().formatted(Formatting.GRAY).append(Texts.toText(e.rawMessage))

                        if (i > 10) mutableText.append(ScreenTexts.ELLIPSIS)
                        mutableText.append(e.input.substring(max(0, i - 10), i))
                        if (i < e.input.length) {
                            val text = Text.literal(e.input.substring(i)).formatted(Formatting.RED, Formatting.UNDERLINE)
                            mutableText.append(text)
                        }
                        mutableText.append(
                            Text.translatable("command.context.here").formatted(Formatting.RED, Formatting.ITALIC)
                        )
                        ChatUtil.send(Text.literal(" ").formatted(Formatting.RED).append(Texts.toText(mutableText)))
                    }
                }
            }
        }
        EventManager.add(InputSuggestionEvent::class) {
            if (it.reader.canRead(PREFIX.length) && it.reader.string.startsWith(PREFIX, it.reader.cursor)) {
                it.reader.cursor += PREFIX.length
                it.dispatcher = dispatcher
                it.commandSource = commandSource
            }
        }
    }

    override fun insert(obj: Command, index: Int) {
        super.insert(obj, index)
        obj.setup(dispatcher)
    }
}