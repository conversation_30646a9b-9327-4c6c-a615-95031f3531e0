package oryx.command

import com.mojang.brigadier.CommandDispatcher
import com.mojang.brigadier.arguments.ArgumentType
import com.mojang.brigadier.builder.LiteralArgumentBuilder
import com.mojang.brigadier.builder.RequiredArgumentBuilder
import net.minecraft.client.network.ClientDynamicRegistryType
import net.minecraft.command.CommandRegistryAccess
import net.minecraft.command.CommandSource
import net.minecraft.resource.featuretoggle.FeatureFlags

abstract class Command(private vararg val aliases: String) {
    companion object {
        const val SUCCESS = 1
        const val ERROR = 0
    }

    private fun literal(name: String): LiteralArgumentBuilder<CommandSource> = LiteralArgumentBuilder.literal(name)
    fun argument(name: String?, type: ArgumentType<*>?): RequiredArgumentBuilder<CommandSource?, *>? =
        RequiredArgumentBuilder.argument(name, type)

    abstract fun builder(builder: LiteralArgumentBuilder<CommandSource>)

    fun setup(dispatcher: CommandDispatcher<CommandSource>) {
        val main = dispatcher.register(literal(aliases[0]).also { builder(it) })
        aliases.forEach { dispatcher.register(literal(it).redirect(main)) }
    }
}