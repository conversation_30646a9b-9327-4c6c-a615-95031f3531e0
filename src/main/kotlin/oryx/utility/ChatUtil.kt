package oryx.utility

import net.minecraft.text.MutableText
import net.minecraft.text.Text
import net.minecraft.text.TextColor
import net.minecraft.util.Formatting
import oryx.Oryx

object ChatUtil : MC {
    fun send(message: String) {
        send(Text.literal(message))
    }

    fun send(message: MutableText) {
        val prefix = buildPrefix()
        prefix.append(message.styled { it.withColor(TextColor.fromRgb(-1)) })
        mc.inGameHud.chatHud.addMessage(prefix)
    }

    private fun buildPrefix(): MutableText {
        return Text.literal("[${Oryx.NAME}]").formatted(Formatting.AQUA).append(" ")
    }
}