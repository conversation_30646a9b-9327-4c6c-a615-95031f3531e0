package oryx.mixin

import com.llamalad7.mixinextras.sugar.Local
import com.mojang.brigadier.ParseResults
import com.mojang.brigadier.StringReader
import com.mojang.brigadier.suggestion.Suggestions
import net.minecraft.client.gui.screen.ChatInputSuggestor
import net.minecraft.client.gui.screen.ChatInputSuggestor.SuggestionWindow
import net.minecraft.client.gui.widget.TextFieldWidget
import net.minecraft.command.CommandSource
import org.spongepowered.asm.mixin.Final
import org.spongepowered.asm.mixin.Mixin
import org.spongepowered.asm.mixin.Shadow
import org.spongepowered.asm.mixin.injection.At
import org.spongepowered.asm.mixin.injection.Inject
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo
import org.spongepowered.asm.mixin.injection.callback.LocalCapture
import oryx.event.EventManager
import oryx.event.impl.InputSuggestionEvent
import java.util.concurrent.CompletableFuture

@Mixin(ChatInputSuggestor::class)
abstract class ChatInputSuggestorMixin {
    @Shadow
    @Final
    lateinit var textField: TextFieldWidget

    @Shadow
    var completingSuggestions: Boolean = false

    @Shadow
    var parse: ParseResults<CommandSource>? = null

    @Shadow
    var window: ChatInputSuggestor.SuggestionWindow? = null

    @Shadow
    var pendingSuggestions: CompletableFuture<*>? = null

    @Shadow
    protected abstract fun showCommandSuggestions()

    @Inject(
        method = ["refresh"],
        at = [At(
            value = "INVOKE",
            target = "Lcom/mojang/brigadier/StringReader;canRead()Z",
            remap = false
        )],
        cancellable = true,
        locals = LocalCapture.CAPTURE_FAILHARD
    )
    fun hookCommandSystem(ci: CallbackInfo, reader: StringReader) {
        val event = InputSuggestionEvent(reader)
        EventManager.post(event)

        val dispatcher = event.dispatcher
        val commandSource = event.commandSource
        if (dispatcher != null && commandSource != null) {
            if (parse == null) parse = dispatcher.parse(reader, commandSource)

            val cursor = textField.cursor
            if (cursor >= 1 && (window == null || !completingSuggestions)) {
                pendingSuggestions = dispatcher.getCompletionSuggestions(parse!!, cursor)
                pendingSuggestions?.thenRun { if (pendingSuggestions!!.isDone) showCommandSuggestions() }
            }

            ci.cancel()
        }
    }
}