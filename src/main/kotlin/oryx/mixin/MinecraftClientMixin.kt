package oryx.mixin

import net.minecraft.client.MinecraftClient
import net.minecraft.client.RunArgs
import org.spongepowered.asm.mixin.Mixin
import org.spongepowered.asm.mixin.Overwrite
import org.spongepowered.asm.mixin.injection.At
import org.spongepowered.asm.mixin.injection.Inject
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo
import oryx.Oryx


@Mixin(MinecraftClient::class)
class MinecraftClientMixin {

    @Overwrite
    private fun getWindowTitle(): String = Oryx.NAME

    @Inject(
        method = ["<init>"],
        at = [At(
            value = "INVOKE",
            target = "Lnet/minecraft/client/MinecraftClient;setOverlay(Lnet/minecraft/client/gui/screen/Overlay;)V",
            shift = At.Shift.AFTER
        )]
    )
    fun lateLoad(args: RunArgs, ci: CallbackInfo) {
        Oryx.load()
    }
}