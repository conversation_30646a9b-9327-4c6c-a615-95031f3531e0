package oryx.module

import org.lwjgl.glfw.GLFW
import oryx.command.CommandManager
import oryx.command.impl.Toggle
import oryx.event.EventManager
import oryx.event.impl.KeyEvent
import oryx.module.move.Sprint
import oryx.utility.Manager

object ModuleManager : Manager<Module>() {
    init {
        add(
            Sprint()
        )

        EventManager.add(KeyEvent::class) {
            for (module in this) {
                if (it.action == GLFW.GLFW_PRESS && it.key == module.key) module.toggle()
            }
        }
    }
}