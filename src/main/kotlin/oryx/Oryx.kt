package oryx

import net.fabricmc.api.ModInitializer
import org.slf4j.LoggerFactory
import oryx.command.CommandManager
import oryx.event.EventManager
import oryx.event.impl.DoneLoadEvent
import oryx.module.ModuleManager

object Oryx : ModInitializer {
    const val NAME = "Oryx"
    private val logger = LoggerFactory.getLogger(NAME)

    override fun onInitialize() {
        logger.info("Welcome to $NAME")
    }

    fun load() {
        ModuleManager
        CommandManager

        EventManager.post(DoneLoadEvent())
    }
}